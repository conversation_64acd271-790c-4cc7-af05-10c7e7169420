<template>
  <div class="device-status-container">
    <!-- 状态图例 -->
    <div class="status-legend">
      <div class="legend-item">
        <div class="online legend-dot"></div>
        <span>在线</span>
      </div>
      <div class="legend-item">
        <div class="legend-dot offline"></div>
        <span>离线</span>
      </div>
      <div class="legend-item">
        <div class="legend-dot error"></div>
        <span>异常</span>
      </div>
    </div>

    <!-- 设备状态网格 -->
    <div class="device-grid">
      <div v-for="device in deviceList" :key="device.name" class="device-item">
        <div class="device-header">
          <span class="device-name">{{ device.name }}</span>
          <div class="device-total">
            {{ device.online + device.offline + device.error }}
          </div>
        </div>
        <div class="status-bars">
          <div class="status-bar">
            <div
              class="online bar-fill"
              :style="{ width: getPercentage(device.online, device) + '%' }"
            ></div>
            <div
              class="bar-fill offline"
              :style="{
                width: getPercentage(device.offline, device) + '%',
                left: getPercentage(device.online, device) + '%',
              }"
            ></div>
            <div
              class="bar-fill error"
              :style="{
                width: getPercentage(device.error, device) + '%',
                left:
                  getPercentage(device.online, device) +
                  getPercentage(device.offline, device) +
                  '%',
              }"
            ></div>
          </div>
          <div class="status-numbers">
            <span class="online count">{{ device.online }}</span>
            <span class="count offline">{{ device.offline }}</span>
            <span class="count error">{{ device.error }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

interface DeviceStatus {
  name: string;
  online: number;
  offline: number;
  error: number;
}

interface Props {
  buildingId: number;
}

const props = defineProps<Props>();

// 根据楼栋ID生成设备状态数据
const generateDeviceStatus = (buildingId: number): DeviceStatus[] => {
  // 基础设备数量，根据楼栋ID有所变化
  const baseMultiplier = 0.8 + (buildingId - 1) * 0.05;

  return [
    {
      name: "闸坝",
      online: Math.floor(3 * baseMultiplier) || 1,
      offline: Math.floor(Math.random() * 2),
      error: Math.floor(Math.random() * 2),
    },
    {
      name: "摄像头",
      online: Math.floor(123 * baseMultiplier),
      offline: Math.floor(4 * baseMultiplier),
      error: Math.floor(5 * baseMultiplier),
    },
    {
      name: "门禁",
      online: Math.floor(8 * baseMultiplier),
      offline: Math.floor(Math.random() * 3),
      error: Math.floor(Math.random() * 2),
    },
    {
      name: "水泵",
      online: Math.floor(24 * baseMultiplier),
      offline: Math.floor(2 * baseMultiplier),
      error: Math.floor(Math.random() * 3),
    },
    {
      name: "水质探头",
      online: Math.floor(36 * baseMultiplier),
      offline: Math.floor(3 * baseMultiplier),
      error: Math.floor(Math.random() * 4),
    },
    {
      name: "风机",
      online: Math.floor(18 * baseMultiplier),
      offline: Math.floor(Math.random() * 3),
      error: Math.floor(Math.random() * 2),
    },
    {
      name: "臭氧",
      online: Math.floor(12 * baseMultiplier),
      offline: Math.floor(Math.random() * 2),
      error: Math.floor(Math.random() * 2),
    },
    {
      name: "消毒灯",
      online: Math.floor(48 * baseMultiplier),
      offline: Math.floor(4 * baseMultiplier),
      error: Math.floor(Math.random() * 3),
    },
  ];
};

const deviceList = ref<DeviceStatus[]>([]);

// 计算百分比
const getPercentage = (count: number, device: DeviceStatus): number => {
  const total = device.online + device.offline + device.error;
  return total > 0 ? (count / total) * 100 : 0;
};

// 监听楼栋变化
watch(
  () => props.buildingId,
  (newBuildingId) => {
    deviceList.value = generateDeviceStatus(newBuildingId);
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.device-status-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 8px;
}

// 状态图例
.status-legend {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding: 8px 12px;
  background: rgba(14, 252, 255, 0.05);
  border: 1px solid rgba(14, 252, 255, 0.2);
  border-radius: 6px;
  margin-bottom: 4px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 11px;
    color: #0efcff;

    .legend-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;

      &.online {
        background: #4fc3f7;
        box-shadow: 0 0 6px rgba(79, 195, 247, 0.6);
      }

      &.offline {
        background: #ffb74d;
        box-shadow: 0 0 6px rgba(255, 183, 77, 0.6);
      }

      &.error {
        background: #f48fb1;
        box-shadow: 0 0 6px rgba(244, 143, 177, 0.6);
      }
    }
  }
}

// 设备网格
.device-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  flex: 1;
  overflow-y: auto;
}

.device-item {
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.1) 0%,
    rgba(102, 204, 255, 0.03) 100%
  );
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 6px;
  padding: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: #66ccff;
    background: linear-gradient(
      135deg,
      rgba(102, 204, 255, 0.18) 0%,
      rgba(102, 204, 255, 0.06) 100%
    );
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 204, 255, 0.25);
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #66ccff, transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .device-name {
    font-size: 12px;
    color: #fff;
    font-weight: 600;
    text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
  }

  .device-total {
    font-size: 11px;
    color: #0efcff;
    background: rgba(14, 252, 255, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
    border: 1px solid rgba(14, 252, 255, 0.3);
    min-width: 20px;
    text-align: center;
  }
}

.status-bars {
  .status-bar {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    position: relative;
    overflow: hidden;
    margin-bottom: 6px;

    .bar-fill {
      position: absolute;
      top: 0;
      height: 100%;
      border-radius: 3px;
      transition: all 0.3s ease;

      &.online {
        background: linear-gradient(90deg, #4fc3f7, #29b6f6);
        box-shadow: 0 0 4px rgba(79, 195, 247, 0.6);
      }

      &.offline {
        background: linear-gradient(90deg, #ffb74d, #ffa726);
        box-shadow: 0 0 4px rgba(255, 183, 77, 0.5);
      }

      &.error {
        background: linear-gradient(90deg, #f48fb1, #f06292);
        box-shadow: 0 0 4px rgba(244, 143, 177, 0.5);
      }
    }
  }

  .status-numbers {
    display: flex;
    justify-content: space-between;
    font-size: 10px;
    font-weight: 600;

    .count {
      &.online {
        color: #4fc3f7;
      }

      &.offline {
        color: #ffb74d;
      }

      &.error {
        color: #f48fb1;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .device-grid {
    grid-template-columns: 1fr;
  }
}
</style>
